/**
 * PFG Core Admin Styles
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */

/* ============================================ *
 * Repository Management Card Layout
 * ============================================ */

#pfg-core-repository-management {
    margin-top: 10px;
}

/* Repository Cards Container */
.pfg-core-repositories-container {
    max-height: 600px;
    overflow-y: auto;
    border: 1px solid #ddd;
    background: #f9f9f9;
    padding: 20px;
    border-radius: 3px;
}

/* Repository Cards Grid */
.pfg-core-repositories-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin: 0;
    padding: 0;
}

/* Individual Repository Card */
.pfg-core-repository-card {
    background: #ffffff;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: box-shadow 0.2s ease, border-color 0.2s ease;
    flex: 1 1 300px;
    min-width: 300px;
    max-width: 450px;
    position: relative;
}

.pfg-core-repository-card:hover {
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    border-color: #ccc;
}

/* Repository Card Header */
.pfg-core-card-header {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.pfg-core-card-title {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin: 0 0 5px 0;
    line-height: 1.3;
}

.pfg-core-card-description {
    font-size: 12px;
    color: #666;
    margin: 0;
    line-height: 1.4;
}

/* Repository Card Body */
.pfg-core-card-body {
    margin-bottom: 15px;
}

.pfg-core-card-info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    font-size: 12px;
}

.pfg-core-card-info-label {
    font-weight: bold;
    color: #555;
    min-width: 60px;
}

.pfg-core-card-info-value {
    color: #333;
    text-align: right;
    flex: 1;
}

/* Status Indicators */
.pfg-core-status-installed {
    color: #3d6611;
    font-weight: bold;
}

.pfg-core-status-installed:before {
    content: "✓ ";
}

.pfg-core-status-update-available {
    color: #f18500;
    font-weight: bold;
}

.pfg-core-status-update-available:before {
    content: "⚠ ";
}

.pfg-core-status-not-installed {
    color: #666;
}

.pfg-core-status-not-installed:before {
    content: "- ";
}

/* Repository Card Actions */
.pfg-core-card-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    align-items: center;
}

.pfg-core-card-actions .scalable {
    font-size: 11px;
    padding: 4px 8px;
    margin: 0;
    min-height: 24px;
    line-height: 16px;
}

.pfg-core-card-actions .scalable span span span {
    padding: 0 4px;
}

/* Action Button Variants */
.pfg-core-btn-install {
    background: #3d6611;
    border-color: #3d6611;
}

.pfg-core-btn-install span span span {
    color: white;
}

.pfg-core-btn-update {
    background: #f18500;
    border-color: #f18500;
}

.pfg-core-btn-update span span span {
    color: white;
}

.pfg-core-btn-remove {
    background: #d9534f;
    border-color: #d43f3a;
}

.pfg-core-btn-remove span span span {
    color: white;
}

.pfg-core-btn-view {
    background: #5bc0de;
    border-color: #46b8da;
}

.pfg-core-btn-view span span span {
    color: white;
}

/* Empty State */
.pfg-core-empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #666;
    font-size: 14px;
}

/* Loading State */
.pfg-core-loading-container {
    text-align: center;
    padding: 40px 20px;
}

.pfg-core-loading-container img {
    margin-bottom: 10px;
}

/* Error State */
.pfg-core-error-container {
    margin-bottom: 15px;
}

/* Responsive Design */
@media screen and (max-width: 768px) {
    .pfg-core-repositories-grid {
        flex-direction: column;
        gap: 15px;
    }
    
    .pfg-core-repository-card {
        flex: 1 1 auto;
        min-width: auto;
        max-width: none;
    }
    
    .pfg-core-card-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .pfg-core-card-actions .scalable {
        width: 100%;
        margin-bottom: 5px;
        text-align: center;
    }
    
    .pfg-core-card-info-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 2px;
    }
    
    .pfg-core-card-info-value {
        text-align: left;
    }
}

@media screen and (max-width: 480px) {
    .pfg-core-repositories-container {
        padding: 15px;
    }
    
    .pfg-core-repository-card {
        padding: 15px;
    }
    
    .pfg-core-card-title {
        font-size: 14px;
    }
    
    .pfg-core-card-description {
        font-size: 11px;
    }
}

/* Integration with Magento Admin Styles */
.pfg-core-repository-card .scalable:hover {
    text-decoration: none;
}

.pfg-core-repository-card .scalable:active {
    position: relative;
    top: 1px;
}

/* Refresh Button Styling */
#pfg-core-repository-management .scalable {
    margin-bottom: 15px;
}

/* Modal Integration */
#pfg-core-install-modal {
    z-index: 1000;
}

#pfg-core-install-modal > div {
    max-width: 500px;
    min-width: 400px;
}

@media screen and (max-width: 480px) {
    #pfg-core-install-modal > div {
        min-width: 300px;
        margin: 20px;
        width: calc(100% - 40px);
        transform: translate(-50%, -50%);
    }
}

<?php
/**
 * PFG Core Repository Management Template
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */

$ajaxUrls = $this->getAjaxUrls();
$formKey = $this->getFormKey();
?>

<div id="pfg-core-repository-management" style="margin-top: 10px;">
    <?php if (!$this->getIsConfigured()): ?>
        <div class="notice-msg">
            <ul>
                <li>
                    <span>
                        <?php echo $this->__('Please configure Bitbucket credentials above to enable repository management.') ?>
                    </span>
                </li>
            </ul>
        </div>
    <?php else: ?>
        <div style="margin-bottom: 15px;">
            <button type="button" onclick="pfgCoreRefreshRepositories()" class="scalable">
                <span><span><span><?php echo $this->__('Refresh Repositories') ?></span></span></span>
            </button>
        </div>
        
        <div id="pfg-core-loading" style="text-align: center; padding: 20px; display: none;">
            <img src="<?php echo $this->getSkinUrl('images/ajax-loader-tr.gif') ?>" alt="<?php echo $this->__('Loading...') ?>" />
            <br />
            <?php echo $this->__('Loading repositories...') ?>
        </div>
        
        <div id="pfg-core-error" style="display: none;" class="error-msg">
            <ul><li><span id="pfg-core-error-message"></span></li></ul>
        </div>
        
        <div id="pfg-core-repositories" style="display: none;">
            <div class="pfg-core-repositories-container">
                <div id="pfg-core-repositories-grid" class="pfg-core-repositories-grid">
                    <!-- Repository cards will be inserted here -->
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Installation Progress Modal -->
<div id="pfg-core-install-modal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
    <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; padding: 20px; border-radius: 5px; min-width: 400px; box-shadow: 0 4px 8px rgba(0,0,0,0.3);">
        <h3 id="pfg-core-install-title"><?php echo $this->__('Installing Module...') ?></h3>
        <div id="pfg-core-install-progress">
            <div style="text-align: center; padding: 20px;">
                <img src="<?php echo $this->getSkinUrl('images/ajax-loader-tr.gif') ?>" alt="<?php echo $this->__('Loading...') ?>" />
                <br />
                <span id="pfg-core-install-status"><?php echo $this->__('Preparing installation...') ?></span>
            </div>
        </div>
        <div id="pfg-core-install-result" style="display: none;">
            <div id="pfg-core-install-message"></div>
            <div style="text-align: right; margin-top: 15px;">
                <button type="button" onclick="pfgCoreCloseInstallModal()" class="scalable">
                    <span><span><span><?php echo $this->__('Close') ?></span></span></span>
                </button>
            </div>
        </div>
    </div>
</div>

<script type="text/javascript">
//<![CDATA[
var pfgCoreAjaxUrls = <?php echo Mage::helper('core')->jsonEncode($ajaxUrls) ?>;
var pfgCoreFormKey = '<?php echo $formKey ?>';

// Load repositories on page load if configured
<?php if ($this->getIsConfigured()): ?>
document.observe('dom:loaded', function() {
    // Delay loading to ensure page is fully rendered
    setTimeout(function() {
        pfgCoreLoadRepositories();
    }, 500);
});
<?php endif; ?>

function pfgCoreRefreshRepositories() {
    pfgCoreLoadRepositories();
}

function pfgCoreLoadRepositories() {
    $('pfg-core-loading').show();
    $('pfg-core-error').hide();
    $('pfg-core-repositories').hide();
    
    new Ajax.Request(pfgCoreAjaxUrls.get_repositories, {
        method: 'post',
        parameters: {
            'form_key': pfgCoreFormKey
        },
        onSuccess: function(response) {
            try {
                var result = response.responseText.evalJSON();
                
                if (result.success) {
                    pfgCoreDisplayRepositories(result.data);
                } else {
                    pfgCoreShowError(result.error || 'Unknown error occurred');
                }
            } catch (e) {
                pfgCoreShowError('Invalid response from server');
            }
        },
        onFailure: function() {
            pfgCoreShowError('Failed to load repositories');
        },
        onComplete: function() {
            $('pfg-core-loading').hide();
        }
    });
}

function pfgCoreDisplayRepositories(repositories) {
    var grid = $('pfg-core-repositories-grid');
    grid.innerHTML = '';

    if (repositories.length === 0) {
        grid.innerHTML = '<div class="pfg-core-empty-state"><?php echo $this->__('No repositories found') ?></div>';
    } else {
        repositories.each(function(repo) {
            var card = pfgCoreCreateRepositoryCard(repo);
            grid.appendChild(card);
        });
    }

    $('pfg-core-repositories').show();
}

function pfgCoreCreateRepositoryCard(repo) {
    var card = document.createElement('div');
    card.className = 'pfg-core-repository-card';

    // Card Header
    var header = document.createElement('div');
    header.className = 'pfg-core-card-header';

    var title = document.createElement('h4');
    title.className = 'pfg-core-card-title';
    title.innerHTML = repo.name.escapeHTML();
    header.appendChild(title);

    if (repo.description) {
        var description = document.createElement('p');
        description.className = 'pfg-core-card-description';
        description.innerHTML = repo.description.escapeHTML();
        header.appendChild(description);
    }

    card.appendChild(header);

    // Card Body
    var body = document.createElement('div');
    body.className = 'pfg-core-card-body';

    // Status Row
    var statusRow = document.createElement('div');
    statusRow.className = 'pfg-core-card-info-row';

    var statusLabel = document.createElement('span');
    statusLabel.className = 'pfg-core-card-info-label';
    statusLabel.innerHTML = '<?php echo $this->__('Status') ?>:';
    statusRow.appendChild(statusLabel);

    var statusValue = document.createElement('span');
    statusValue.className = 'pfg-core-card-info-value';
    switch (repo.installation_status) {
        case 'installed':
            statusValue.className += ' pfg-core-status-installed';
            statusValue.innerHTML = '<?php echo $this->__('Installed') ?>';
            break;
        case 'update_available':
            statusValue.className += ' pfg-core-status-update-available';
            statusValue.innerHTML = '<?php echo $this->__('Update Available') ?>';
            break;
        case 'not_installed':
            statusValue.className += ' pfg-core-status-not-installed';
            statusValue.innerHTML = '<?php echo $this->__('Not Installed') ?>';
            break;
    }
    statusRow.appendChild(statusValue);
    body.appendChild(statusRow);

    // Version Information
    if (repo.installed_version || repo.latest_version) {
        if (repo.installed_version) {
            var currentVersionRow = document.createElement('div');
            currentVersionRow.className = 'pfg-core-card-info-row';

            var currentLabel = document.createElement('span');
            currentLabel.className = 'pfg-core-card-info-label';
            currentLabel.innerHTML = '<?php echo $this->__('Current') ?>:';
            currentVersionRow.appendChild(currentLabel);

            var currentValue = document.createElement('span');
            currentValue.className = 'pfg-core-card-info-value';
            currentValue.innerHTML = repo.installed_version.escapeHTML();
            currentVersionRow.appendChild(currentValue);
            body.appendChild(currentVersionRow);
        }

        if (repo.latest_version) {
            var latestVersionRow = document.createElement('div');
            latestVersionRow.className = 'pfg-core-card-info-row';

            var latestLabel = document.createElement('span');
            latestLabel.className = 'pfg-core-card-info-label';
            latestLabel.innerHTML = '<?php echo $this->__('Latest') ?>:';
            latestVersionRow.appendChild(latestLabel);

            var latestValue = document.createElement('span');
            latestValue.className = 'pfg-core-card-info-value';
            latestValue.innerHTML = repo.latest_version.escapeHTML();
            latestVersionRow.appendChild(latestValue);
            body.appendChild(latestVersionRow);
        }
    }

    card.appendChild(body);

    // Card Actions
    var actions = document.createElement('div');
    actions.className = 'pfg-core-card-actions';

    // Show install button if not installed
    if (!repo.is_installed && repo.latest_version) {
        var installBtn = document.createElement('button');
        installBtn.type = 'button';
        installBtn.className = 'scalable pfg-core-btn-install';
        installBtn.onclick = function() { pfgCoreInstallModule(repo.name, repo.latest_version); };
        installBtn.innerHTML = '<span><span><span><?php echo $this->__('Install') ?></span></span></span>';
        actions.appendChild(installBtn);
    }

    // Show update button if update is available
    if (repo.has_update && repo.latest_version) {
        var updateBtn = document.createElement('button');
        updateBtn.type = 'button';
        updateBtn.className = 'scalable pfg-core-btn-update';
        updateBtn.onclick = function() { pfgCoreUpdateModule(repo.name, repo.latest_version); };
        updateBtn.innerHTML = '<span><span><span><?php echo $this->__('Update') ?></span></span></span>';
        actions.appendChild(updateBtn);
    }

    // Show complete removal button if installed
    if (repo.is_installed && repo.module_name) {
        var removeBtn = document.createElement('button');
        removeBtn.type = 'button';
        removeBtn.className = 'scalable pfg-core-btn-remove';
        removeBtn.onclick = function() { pfgCoreCompleteRemoval(repo.module_name); };
        removeBtn.innerHTML = '<span><span><span><?php echo $this->__('Complete Removal') ?></span></span></span>';
        actions.appendChild(removeBtn);
    }

    // Show view button if URL is available
    if (repo.html_url) {
        var viewBtn = document.createElement('button');
        viewBtn.type = 'button';
        viewBtn.className = 'scalable pfg-core-btn-view';
        viewBtn.onclick = function() { window.open(repo.html_url, '_blank'); };
        viewBtn.innerHTML = '<span><span><span><?php echo $this->__('View') ?></span></span></span>';
        actions.appendChild(viewBtn);
    }

    card.appendChild(actions);

    return card;
}

function pfgCoreInstallModule(repositoryName, version) {
    if (!confirm('<?php echo $this->__('Are you sure you want to install this module? This action will create a backup and install the module files.') ?>')) {
        return;
    }
    
    pfgCoreShowInstallModal('<?php echo $this->__('Installing Module') ?>', '<?php echo $this->__('Preparing installation...') ?>');
    
    new Ajax.Request(pfgCoreAjaxUrls.install_module, {
        method: 'post',
        parameters: {
            'form_key': pfgCoreFormKey,
            'repository': repositoryName,
            'version': version
        },
        onSuccess: function(response) {
            pfgCoreHandleInstallResponse(response);
        },
        onFailure: function() {
            pfgCoreShowInstallResult(false, '<?php echo $this->__('Installation failed') ?>');
        }
    });
}

function pfgCoreUpdateModule(repositoryName, version) {
    if (!confirm('<?php echo $this->__('Are you sure you want to update this module? This action will create a backup and update the module files.') ?>')) {
        return;
    }
    
    pfgCoreShowInstallModal('<?php echo $this->__('Updating Module') ?>', '<?php echo $this->__('Preparing update...') ?>');
    
    new Ajax.Request(pfgCoreAjaxUrls.update_module, {
        method: 'post',
        parameters: {
            'form_key': pfgCoreFormKey,
            'repository': repositoryName,
            'version': version
        },
        onSuccess: function(response) {
            pfgCoreHandleInstallResponse(response);
        },
        onFailure: function() {
            pfgCoreShowInstallResult(false, '<?php echo $this->__('Update failed') ?>');
        }
    });
}

function pfgCoreHandleInstallResponse(response) {
    try {
        var result = response.responseText.evalJSON();
        
        if (result.success) {
            pfgCoreShowInstallResult(true, result.message);
            // Trigger post-installation actions
            pfgCorePostInstallActions();
        } else {
            pfgCoreShowInstallResult(false, result.error || '<?php echo $this->__('Installation failed') ?>');
        }
    } catch (e) {
        pfgCoreShowInstallResult(false, '<?php echo $this->__('Invalid response from server') ?>');
    }
}

function pfgCorePostInstallActions() {
    $('pfg-core-install-status').innerHTML = '<?php echo $this->__('Clearing caches and performing cleanup...') ?>';
    
    new Ajax.Request(pfgCoreAjaxUrls.post_install_actions, {
        method: 'post',
        parameters: {
            'form_key': pfgCoreFormKey
        },
        onComplete: function() {
            // Refresh repositories list
            setTimeout(function() {
                pfgCoreLoadRepositories();
            }, 1000);
        }
    });
}

function pfgCoreShowInstallModal(title, status) {
    $('pfg-core-install-title').innerHTML = title;
    $('pfg-core-install-status').innerHTML = status;
    $('pfg-core-install-progress').show();
    $('pfg-core-install-result').hide();
    $('pfg-core-install-modal').show();
}

function pfgCoreShowInstallResult(success, message) {
    $('pfg-core-install-progress').hide();
    
    var messageDiv = $('pfg-core-install-message');
    if (success) {
        messageDiv.className = 'success-msg';
        messageDiv.innerHTML = '<ul><li><span>' + message.escapeHTML() + '</span></li></ul>';
    } else {
        messageDiv.className = 'error-msg';
        messageDiv.innerHTML = '<ul><li><span>' + message.escapeHTML() + '</span></li></ul>';
    }
    
    $('pfg-core-install-result').show();
}

function pfgCoreCloseInstallModal() {
    $('pfg-core-install-modal').hide();
}

function pfgCoreShowError(message) {
    $('pfg-core-error-message').innerHTML = message.escapeHTML();
    $('pfg-core-error').show();
}

function pfgCoreCompleteRemoval(moduleName) {
    if (!confirm('<?php echo $this->__('Are you sure you want to completely remove this module? This action will:\n\n• Remove all module files\n• Delete all database tables\n• Remove all configuration settings\n• Clear all caches\n• Revert system to pre-installation state\n\nThis action cannot be undone!') ?>')) {
        return;
    }

    pfgCoreShowInstallModal('<?php echo $this->__('Complete Module Removal') ?>', '<?php echo $this->__('Creating backup and completely removing module...') ?>');

    new Ajax.Request(pfgCoreAjaxUrls.rollback, {
        method: 'post',
        parameters: {
            'form_key': pfgCoreFormKey,
            'module': moduleName
        },
        onSuccess: function(response) {
            pfgCoreHandleInstallResponse(response);
        },
        onFailure: function() {
            pfgCoreShowInstallResult(false, '<?php echo $this->__('Complete removal failed') ?>');
        }
    });
}


//]]>
</script>

<?php
/**
 * PFG Core Admin Controller
 * 
 * @category   PFG
 * @package    PFG_Core
 * <AUTHOR> Development Team
 * @copyright  Copyright (c) 2025 PFG
 * @license    http://opensource.org/licenses/osl-3.0.php  Open Software License (OSL 3.0)
 */
class PFG_Core_Adminhtml_Pfg_CoreController extends Mage_Adminhtml_Controller_Action
{
    /**
     * Test Bitbucket connection
     */
    public function testConnectionAction()
    {
        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                throw new Exception($this->__('Invalid form key. Please refresh the page.'));
            }
            
            $request = $this->getRequest();
            
            // Get credentials from request (for testing without saving)
            $username = $request->getParam('username');
            $appPassword = $request->getParam('app_password');
            $workspace = $request->getParam('workspace', 'pfg');
            $project = $request->getParam('project', 'LABS');

            // Debug logging
            Mage::helper('pfg_core')->log('Test connection params - Username: ' . $username . ', Password length: ' . strlen($appPassword) . ', Workspace: ' . $workspace, Zend_Log::DEBUG);

            // If any field is empty, try to get from stored config
            if (empty($username)) {
                $username = Mage::helper('pfg_core')->getBitbucketUsername();
            }
            if (empty($appPassword) || $appPassword === '******') {
                $appPassword = Mage::helper('pfg_core')->getBitbucketAppPassword();
            }
            if (empty($workspace)) {
                $workspace = Mage::helper('pfg_core')->getBitbucketWorkspace();
            }
            if (empty($project)) {
                $project = Mage::helper('pfg_core')->getBitbucketProject();
            }

            // Validate and sanitize inputs
            $username = $this->_validateAndSanitizeBitbucketUsername($username);
            $appPassword = $this->_validateAndSanitizeBitbucketPassword($appPassword);
            $workspace = $this->_validateAndSanitizeWorkspace($workspace);
            $project = $this->_validateAndSanitizeProject($project);
            
            // Temporarily set credentials for testing
            $originalUsername = Mage::getStoreConfig('pfg/core/bitbucket_username');
            $originalPassword = Mage::getStoreConfig('pfg/core/bitbucket_app_password');
            $originalWorkspace = Mage::getStoreConfig('pfg/core/bitbucket_workspace');
            $originalProject = Mage::getStoreConfig('pfg/core/bitbucket_project');
            
            // Set test values
            Mage::app()->getStore()->setConfig('pfg/core/bitbucket_username', $username);
            Mage::app()->getStore()->setConfig('pfg/core/bitbucket_app_password', Mage::helper('core')->encrypt($appPassword));
            Mage::app()->getStore()->setConfig('pfg/core/bitbucket_workspace', $workspace);
            Mage::app()->getStore()->setConfig('pfg/core/bitbucket_project', $project);
            
            // Test connection
            $bitbucketHelper = Mage::helper('pfg_core/bitbucket');
            $result = $bitbucketHelper->testConnection();
            
            // Restore original values
            Mage::app()->getStore()->setConfig('pfg/core/bitbucket_username', $originalUsername);
            Mage::app()->getStore()->setConfig('pfg/core/bitbucket_app_password', $originalPassword);
            Mage::app()->getStore()->setConfig('pfg/core/bitbucket_workspace', $originalWorkspace);
            Mage::app()->getStore()->setConfig('pfg/core/bitbucket_project', $originalProject);
            
            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
            
        } catch (Exception $e) {
            Mage::helper('pfg_core')->log('Connection test error: ' . $e->getMessage(), Zend_Log::ERR);
            
            $result = array(
                'success' => false,
                'message' => $e->getMessage()
            );
            
            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
        }
    }
    
    /**
     * Main module manager page - DEPRECATED
     * Functionality moved to System Configuration
     */
    public function indexAction()
    {
        $this->_redirect('adminhtml/system_config/edit/section/pfg');
    }

    /**
     * Repository browser page - DEPRECATED
     * Functionality moved to System Configuration
     */
    public function repositoriesAction()
    {
        $this->_redirect('adminhtml/system_config/edit/section/pfg');
    }
    
    /**
     * Get repositories via AJAX
     */
    public function getRepositoriesAction()
    {
        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                throw new Exception($this->__('Invalid form key. Please refresh the page.'));
            }
            
            $repositoryModel = Mage::getModel('pfg_core/repository');
            $result = $repositoryModel->getRepositoriesWithStatus();
            
            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
            
        } catch (Exception $e) {
            Mage::helper('pfg_core')->log('Get repositories error: ' . $e->getMessage(), Zend_Log::ERR);
            
            $result = array(
                'success' => false,
                'error' => $e->getMessage()
            );
            
            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
        }
    }
    
    /**
     * Install module action
     */
    public function installModuleAction()
    {
        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                throw new Exception($this->__('Invalid form key. Please refresh the page.'));
            }
            
            $request = $this->getRequest();
            $repositoryName = $request->getParam('repository');
            $version = $request->getParam('version', 'master');

            // Validate and sanitize inputs
            $repositoryName = $this->_validateAndSanitizeRepositoryName($repositoryName);
            $version = $this->_validateAndSanitizeVersion($version);
            
            $installationModel = Mage::getModel('pfg_core/installation');
            $result = $installationModel->installModule($repositoryName, $version);
            
            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
            
        } catch (Exception $e) {
            Mage::helper('pfg_core')->log('Install module error: ' . $e->getMessage(), Zend_Log::ERR);
            
            $result = array(
                'success' => false,
                'error' => $e->getMessage()
            );
            
            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
        }
    }
    
    /**
     * Update module action
     */
    public function updateModuleAction()
    {
        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                throw new Exception($this->__('Invalid form key. Please refresh the page.'));
            }
            
            $request = $this->getRequest();
            $repositoryName = $request->getParam('repository');
            $version = $request->getParam('version');

            // Validate and sanitize inputs
            $repositoryName = $this->_validateAndSanitizeRepositoryName($repositoryName);
            $version = $this->_validateAndSanitizeVersion($version);

            if (empty($version)) {
                throw new Exception($this->__('Version is required for updates'));
            }
            
            $installationModel = Mage::getModel('pfg_core/installation');
            $result = $installationModel->updateModule($repositoryName, $version);
            
            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
            
        } catch (Exception $e) {
            Mage::helper('pfg_core')->log('Update module error: ' . $e->getMessage(), Zend_Log::ERR);
            
            $result = array(
                'success' => false,
                'error' => $e->getMessage()
            );
            
            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
        }
    }



    /**
     * Installation history page - DEPRECATED
     * Functionality moved to System Configuration
     */
    public function installationsAction()
    {
        $this->_redirect('adminhtml/system_config/edit/section/pfg');
    }

    /**
     * Backup management page - DEPRECATED
     * Functionality moved to System Configuration
     */
    public function backupsAction()
    {
        $this->_redirect('adminhtml/system_config/edit/section/pfg');
    }
    
    /**
     * Complete module removal action (rollback)
     * This action completely removes a module and reverts everything to pre-installation state
     */
    public function rollbackAction()
    {
        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                throw new Exception($this->__('Invalid form key. Please refresh the page.'));
            }

            $request = $this->getRequest();
            $moduleName = $request->getParam('module');
            $installationId = $request->getParam('installation_id');

            // Validate and sanitize inputs
            if (!empty($moduleName)) {
                $moduleName = $this->_validateAndSanitizeModuleName($moduleName);
            }
            if (!empty($installationId)) {
                $installationId = $this->_validateAndSanitizeId($installationId);
            }

            if (empty($moduleName) && empty($installationId)) {
                throw new Exception($this->__('Module name or Installation ID is required'));
            }

            $installationModel = Mage::getModel('pfg_core/installation');

            // If installation ID is provided, load the specific installation
            if (!empty($installationId)) {
                $installationModel->load($installationId);
                if (!$installationModel->getId()) {
                    throw new Exception($this->__('Installation not found'));
                }
                $moduleName = $installationModel->getModuleName();
            }

            // Perform complete module removal (enhanced rollback)
            $result = $installationModel->completeModuleRemoval($moduleName);

            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));

        } catch (Exception $e) {
            Mage::helper('pfg_core')->log('Complete module removal error: ' . $e->getMessage(), Zend_Log::ERR);

            $result = array(
                'success' => false,
                'error' => $e->getMessage()
            );

            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
        }
    }
    
    /**
     * Clear caches and logout admin users
     */
    public function postInstallActionsAction()
    {
        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                throw new Exception($this->__('Invalid form key. Please refresh the page.'));
            }

            $logger = Mage::getModel('pfg_core/logger');
            $postInstallModel = Mage::getModel('pfg_core/postInstall');

            $logger->logInstallation('post_install_started', 'system', 'Starting post-installation cleanup');

            $result = $postInstallModel->executePostInstallActions();

            if ($result['success']) {
                $logger->logInstallation('post_install_completed', 'system', 'Post-installation cleanup completed successfully');
            } else {
                $logger->logInstallation('post_install_failed', 'system', $result['error'], PFG_Core_Model_Logger::LOG_LEVEL_ERROR);
            }

            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));

        } catch (Exception $e) {
            $errorHandler = Mage::getModel('pfg_core/errorHandler');
            $result = $errorHandler->handleInstallationError($e, 'system', 'post_install');

            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
        }
    }
    
    /**
     * View installation details
     */
    public function viewInstallationAction()
    {
        $id = $this->getRequest()->getParam('id');
        $installation = Mage::getModel('pfg_core/installation')->load($id);

        if (!$installation->getId()) {
            $this->_getSession()->addError($this->__('Installation not found'));
            $this->_redirect('*/*/installations');
            return;
        }

        Mage::register('current_installation', $installation);

        $this->_title($this->__('PFG'))->_title($this->__('Installation Details'));

        $this->loadLayout();
        $this->_setActiveMenu('pfg/installations');
        $this->_addBreadcrumb($this->__('PFG'), $this->__('PFG'));
        $this->_addBreadcrumb($this->__('Installation History'), $this->__('Installation History'));
        $this->_addBreadcrumb($this->__('Installation Details'), $this->__('Installation Details'));

        $this->renderLayout();
    }

    /**
     * Cleanup old installation records
     */
    public function cleanupInstallationsAction()
    {
        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                throw new Exception($this->__('Invalid form key. Please refresh the page.'));
            }

            $cutoffDate = date('Y-m-d H:i:s', strtotime('-30 days'));

            $collection = Mage::getModel('pfg_core/installation')->getCollection()
                ->addFieldToFilter('created_at', array('lt' => $cutoffDate))
                ->addFieldToFilter('installation_status', array('in' => array(
                    PFG_Core_Model_Installation::STATUS_COMPLETED,
                    PFG_Core_Model_Installation::STATUS_FAILED,
                    PFG_Core_Model_Installation::STATUS_ROLLED_BACK
                )));

            $deletedCount = 0;
            foreach ($collection as $installation) {
                $installation->delete();
                $deletedCount++;
            }

            $this->_getSession()->addSuccess($this->__('Cleaned up %d old installation records', $deletedCount));

        } catch (Exception $e) {
            $this->_getSession()->addError($this->__('Failed to cleanup installation records: %s', $e->getMessage()));
        }

        $this->_redirect('*/*/installations');
    }

    /**
     * Mass delete installation records
     */
    public function massDeleteInstallationsAction()
    {
        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                throw new Exception($this->__('Invalid form key. Please refresh the page.'));
            }

            $installationIds = $this->getRequest()->getParam('installation_ids');

            if (!is_array($installationIds) || empty($installationIds)) {
                throw new Exception($this->__('Please select installation records to delete'));
            }

            $deletedCount = 0;
            foreach ($installationIds as $id) {
                $installation = Mage::getModel('pfg_core/installation')->load($id);
                if ($installation->getId()) {
                    $installation->delete();
                    $deletedCount++;
                }
            }

            $this->_getSession()->addSuccess($this->__('Deleted %d installation records', $deletedCount));

        } catch (Exception $e) {
            $this->_getSession()->addError($this->__('Failed to delete installation records: %s', $e->getMessage()));
        }

        $this->_redirect('*/*/installations');
    }

    /**
     * Download backup file
     */
    public function downloadBackupAction()
    {
        try {
            $id = $this->getRequest()->getParam('id');
            $backup = Mage::getModel('pfg_core/backup')->load($id);

            if (!$backup->getId()) {
                throw new Exception($this->__('Backup not found'));
            }

            $filePath = $backup->getBackupPath();

            if (!file_exists($filePath)) {
                throw new Exception($this->__('Backup file not found'));
            }

            $fileName = basename($filePath);

            $this->getResponse()
                ->setHttpResponseCode(200)
                ->setHeader('Pragma', 'public', true)
                ->setHeader('Cache-Control', 'must-revalidate, post-check=0, pre-check=0', true)
                ->setHeader('Content-type', 'application/octet-stream', true)
                ->setHeader('Content-Length', filesize($filePath), true)
                ->setHeader('Content-Disposition', 'attachment; filename="' . $fileName . '"', true)
                ->setHeader('Last-Modified', date('r', filemtime($filePath)), true);

            $this->getResponse()->clearBody();
            $this->getResponse()->sendHeaders();

            readfile($filePath);
            exit;

        } catch (Exception $e) {
            $this->_getSession()->addError($this->__('Failed to download backup: %s', $e->getMessage()));
            $this->_redirect('*/*/backups');
        }
    }

    /**
     * Restore from backup
     */
    public function restoreBackupAction()
    {
        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                throw new Exception($this->__('Invalid form key. Please refresh the page.'));
            }

            $id = $this->getRequest()->getParam('id');
            $backup = Mage::getModel('pfg_core/backup')->load($id);

            if (!$backup->getId()) {
                throw new Exception($this->__('Backup not found'));
            }

            $result = $backup->restoreBackup($id);

            if ($result['success']) {
                $this->_getSession()->addSuccess($result['message']);
            } else {
                throw new Exception($result['error']);
            }

        } catch (Exception $e) {
            $this->_getSession()->addError($this->__('Failed to restore backup: %s', $e->getMessage()));
        }

        $this->_redirect('*/*/backups');
    }

    /**
     * Cleanup old backups
     */
    public function cleanupBackupsAction()
    {
        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                throw new Exception($this->__('Invalid form key. Please refresh the page.'));
            }

            $backupModel = Mage::getModel('pfg_core/backup');
            $result = $backupModel->cleanupOldBackups();

            if ($result['success']) {
                $this->_getSession()->addSuccess($this->__('Cleaned up %d old backups, freed %s',
                    $result['deleted_count'],
                    Mage::helper('pfg_core')->formatFileSize($result['freed_space'])
                ));
            } else {
                throw new Exception($result['error']);
            }

        } catch (Exception $e) {
            $this->_getSession()->addError($this->__('Failed to cleanup backups: %s', $e->getMessage()));
        }

        $this->_redirect('*/*/backups');
    }

    /**
     * Mass delete backups
     */
    public function massDeleteBackupsAction()
    {
        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                throw new Exception($this->__('Invalid form key. Please refresh the page.'));
            }

            $backupIds = $this->getRequest()->getParam('backup_ids');

            if (!is_array($backupIds) || empty($backupIds)) {
                throw new Exception($this->__('Please select backups to delete'));
            }

            $deletedCount = 0;
            $freedSpace = 0;

            foreach ($backupIds as $id) {
                $backup = Mage::getModel('pfg_core/backup')->load($id);
                if ($backup->getId()) {
                    if (file_exists($backup->getBackupPath())) {
                        $freedSpace += filesize($backup->getBackupPath());
                        unlink($backup->getBackupPath());
                    }
                    $backup->delete();
                    $deletedCount++;
                }
            }

            $this->_getSession()->addSuccess($this->__('Deleted %d backups, freed %s',
                $deletedCount,
                Mage::helper('pfg_core')->formatFileSize($freedSpace)
            ));

        } catch (Exception $e) {
            $this->_getSession()->addError($this->__('Failed to delete backups: %s', $e->getMessage()));
        }

        $this->_redirect('*/*/backups');
    }

    /**
     * System logs page - DEPRECATED
     * Functionality moved to System Configuration
     */
    public function logsAction()
    {
        $this->_redirect('adminhtml/system_config/edit/section/pfg');
    }

    /**
     * Log grid for AJAX - DEPRECATED
     * Functionality moved to System Configuration
     */
    public function logGridAction()
    {
        $this->_redirect('adminhtml/system_config/edit/section/pfg');
    }

    /**
     * View log details
     */
    public function viewLogAction()
    {
        $id = $this->getRequest()->getParam('id');

        try {
            $connection = Mage::getSingleton('core/resource')->getConnection('core_read');
            $table = Mage::getSingleton('core/resource')->getTableName('pfg_core_audit_log');

            $logEntry = $connection->fetchRow(
                $connection->select()->from($table)->where('log_id = ?', $id)
            );

            if (!$logEntry) {
                throw new Exception('Log entry not found');
            }

            Mage::register('current_log_entry', $logEntry);

            $this->_title($this->__('PFG'))->_title($this->__('Log Details'));

            $this->loadLayout();
            $this->_setActiveMenu('pfg/logs');
            $this->_addBreadcrumb($this->__('PFG'), $this->__('PFG'));
            $this->_addBreadcrumb($this->__('System Logs'), $this->__('System Logs'));
            $this->_addBreadcrumb($this->__('Log Details'), $this->__('Log Details'));

            $this->renderLayout();

        } catch (Exception $e) {
            $this->_getSession()->addError($this->__('Error loading log entry: %s', $e->getMessage()));
            $this->_redirect('*/*/logs');
        }
    }

    /**
     * Clear old logs
     */
    public function clearLogsAction()
    {
        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                throw new Exception($this->__('Invalid form key. Please refresh the page.'));
            }

            $cutoffDate = date('Y-m-d H:i:s', strtotime('-7 days'));

            $connection = Mage::getSingleton('core/resource')->getConnection('core_write');
            $table = Mage::getSingleton('core/resource')->getTableName('pfg_core_audit_log');

            $deletedCount = $connection->delete($table, array('created_at < ?' => $cutoffDate));

            $this->_getSession()->addSuccess($this->__('Cleared %d old log entries', $deletedCount));

        } catch (Exception $e) {
            $this->_getSession()->addError($this->__('Failed to clear logs: %s', $e->getMessage()));
        }

        $this->_redirect('*/*/logs');
    }

    /**
     * Download logs as CSV
     */
    public function downloadLogsAction()
    {
        try {
            $connection = Mage::getSingleton('core/resource')->getConnection('core_read');
            $table = Mage::getSingleton('core/resource')->getTableName('pfg_core_audit_log');

            $logs = $connection->fetchAll(
                $connection->select()
                    ->from($table)
                    ->order('created_at DESC')
                    ->limit(1000)
            );

            $filename = 'pfg_core_logs_' . date('Y-m-d_H-i-s') . '.csv';

            $this->getResponse()
                ->setHttpResponseCode(200)
                ->setHeader('Pragma', 'public', true)
                ->setHeader('Cache-Control', 'must-revalidate, post-check=0, pre-check=0', true)
                ->setHeader('Content-type', 'application/csv', true)
                ->setHeader('Content-Disposition', 'attachment; filename="' . $filename . '"', true);

            $this->getResponse()->clearBody();

            // Output CSV headers
            echo "ID,Timestamp,Level,Type,Message,User ID,IP Address\n";

            // Output log data
            foreach ($logs as $log) {
                echo sprintf('"%s","%s","%s","%s","%s","%s","%s"' . "\n",
                    $log['log_id'],
                    $log['created_at'],
                    $log['level'],
                    $log['type'],
                    str_replace('"', '""', $log['message']),
                    $log['user_id'],
                    $log['ip_address']
                );
            }

        } catch (Exception $e) {
            $this->_getSession()->addError($this->__('Failed to download logs: %s', $e->getMessage()));
            $this->_redirect('*/*/logs');
        }
    }

    /**
     * Mass delete logs
     */
    public function massDeleteLogsAction()
    {
        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                throw new Exception($this->__('Invalid form key. Please refresh the page.'));
            }

            $logIds = $this->getRequest()->getParam('log_ids');

            if (!is_array($logIds) || empty($logIds)) {
                throw new Exception($this->__('Please select log entries to delete'));
            }

            $connection = Mage::getSingleton('core/resource')->getConnection('core_write');
            $table = Mage::getSingleton('core/resource')->getTableName('pfg_core_audit_log');

            $deletedCount = $connection->delete($table, array('log_id IN (?)' => $logIds));

            $this->_getSession()->addSuccess($this->__('Deleted %d log entries', $deletedCount));

        } catch (Exception $e) {
            $this->_getSession()->addError($this->__('Failed to delete log entries: %s', $e->getMessage()));
        }

        $this->_redirect('*/*/logs');
    }

    /**
     * Get installation history via AJAX
     */
    public function getInstallationHistoryAction()
    {
        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                throw new Exception($this->__('Invalid form key. Please refresh the page.'));
            }

            // Get recent installations
            $installations = Mage::getModel('pfg_core/installation')->getCollection()
                ->setOrder('created_at', 'DESC')
                ->setPageSize(10);

            // Render the content HTML
            $block = $this->getLayout()->createBlock('core/template')
                ->setTemplate('pfg_core/system/config/installation/history_content.phtml')
                ->setInstallations($installations);

            $html = $block->toHtml();

            $result = array(
                'success' => true,
                'html' => $html
            );

            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));

        } catch (Exception $e) {
            Mage::helper('pfg_core')->log('Get installation history error: ' . $e->getMessage(), Zend_Log::ERR);

            $result = array(
                'success' => false,
                'error' => $e->getMessage()
            );

            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
        }
    }

    /**
     * Get system logs via AJAX
     */
    public function getSystemLogsAction()
    {
        try {
            // Validate form key for CSRF protection
            if (!$this->_validateFormKey()) {
                throw new Exception($this->__('Invalid form key. Please refresh the page.'));
            }

            // Get recent log entries
            try {
                $connection = Mage::getSingleton('core/resource')->getConnection('core_read');
                $table = Mage::getSingleton('core/resource')->getTableName('pfg_core_audit_log');

                $logs = array();
                if ($connection->isTableExists($table)) {
                    $select = $connection->select()
                        ->from($table)
                        ->order('created_at DESC')
                        ->limit(20);

                    $logs = $connection->fetchAll($select);
                }
            } catch (Exception $e) {
                $logs = array();
            }

            // Render the content HTML
            $block = $this->getLayout()->createBlock('core/template')
                ->setTemplate('pfg_core/system/config/system/logs_content.phtml')
                ->setLogs($logs);

            $html = $block->toHtml();

            $result = array(
                'success' => true,
                'html' => $html
            );

            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));

        } catch (Exception $e) {
            Mage::helper('pfg_core')->log('Get system logs error: ' . $e->getMessage(), Zend_Log::ERR);

            $result = array(
                'success' => false,
                'error' => $e->getMessage()
            );

            $this->getResponse()->setBody(Mage::helper('core')->jsonEncode($result));
        }
    }

    /**
     * Check admin permissions
     * Since PFG Core is now only accessible through system configuration,
     * we check for system config permissions
     *
     * @return bool
     */
    protected function _isAllowed()
    {
        return Mage::getSingleton('admin/session')->isAllowed('system/config/pfg/core');
    }

    /**
     * Validate and sanitize repository name
     *
     * @param string $repositoryName
     * @return string
     * @throws Exception
     */
    protected function _validateAndSanitizeRepositoryName($repositoryName)
    {
        if (empty($repositoryName)) {
            throw new Exception($this->__('Repository name is required'));
        }

        // Remove any whitespace
        $repositoryName = trim($repositoryName);

        // Validate format: only letters, numbers, hyphens, and underscores
        if (!preg_match('/^[a-zA-Z0-9_-]+$/', $repositoryName)) {
            throw new Exception($this->__('Repository name can only contain letters, numbers, hyphens, and underscores'));
        }

        // Check length constraints
        if (strlen($repositoryName) < 2) {
            throw new Exception($this->__('Repository name must be at least 2 characters long'));
        }

        if (strlen($repositoryName) > 100) {
            throw new Exception($this->__('Repository name cannot exceed 100 characters'));
        }

        // Prevent path traversal attempts
        if (strpos($repositoryName, '..') !== false || strpos($repositoryName, '/') !== false || strpos($repositoryName, '\\') !== false) {
            throw new Exception($this->__('Repository name contains invalid characters'));
        }

        return $repositoryName;
    }

    /**
     * Validate and sanitize version string
     *
     * @param string $version
     * @return string
     * @throws Exception
     */
    protected function _validateAndSanitizeVersion($version)
    {
        if (empty($version)) {
            $version = 'master'; // Default version
        }

        // Remove any whitespace
        $version = trim($version);

        // Validate format: letters, numbers, dots, hyphens, underscores
        if (!preg_match('/^[a-zA-Z0-9._-]+$/', $version)) {
            throw new Exception($this->__('Version can only contain letters, numbers, dots, hyphens, and underscores'));
        }

        // Check length constraints
        if (strlen($version) > 50) {
            throw new Exception($this->__('Version cannot exceed 50 characters'));
        }

        // Prevent path traversal attempts
        if (strpos($version, '..') !== false || strpos($version, '/') !== false || strpos($version, '\\') !== false) {
            throw new Exception($this->__('Version contains invalid characters'));
        }

        return $version;
    }

    /**
     * Validate and sanitize module name
     *
     * @param string $moduleName
     * @return string
     * @throws Exception
     */
    protected function _validateAndSanitizeModuleName($moduleName)
    {
        if (empty($moduleName)) {
            throw new Exception($this->__('Module name is required'));
        }

        // Remove any whitespace
        $moduleName = trim($moduleName);

        // Validate Magento module name format: Namespace_ModuleName
        if (!preg_match('/^[A-Z][a-zA-Z0-9]*_[A-Z][a-zA-Z0-9]*$/', $moduleName)) {
            throw new Exception($this->__('Module name must follow Magento naming convention (Namespace_ModuleName)'));
        }

        // Check length constraints
        if (strlen($moduleName) > 100) {
            throw new Exception($this->__('Module name cannot exceed 100 characters'));
        }

        // Prevent path traversal attempts
        if (strpos($moduleName, '..') !== false || strpos($moduleName, '/') !== false || strpos($moduleName, '\\') !== false) {
            throw new Exception($this->__('Module name contains invalid characters'));
        }

        return $moduleName;
    }

    /**
     * Validate and sanitize ID parameter
     *
     * @param mixed $id
     * @return int
     * @throws Exception
     */
    protected function _validateAndSanitizeId($id)
    {
        if (empty($id)) {
            throw new Exception($this->__('ID is required'));
        }

        // Convert to integer and validate
        $id = (int) $id;

        if ($id <= 0) {
            throw new Exception($this->__('ID must be a positive integer'));
        }

        if ($id > 2147483647) { // Max INT value
            throw new Exception($this->__('ID value is too large'));
        }

        return $id;
    }

    /**
     * Validate and sanitize Bitbucket username
     *
     * @param string $username
     * @return string
     * @throws Exception
     */
    protected function _validateAndSanitizeBitbucketUsername($username)
    {
        if (empty($username)) {
            throw new Exception($this->__('Bitbucket username is required'));
        }

        // Remove any whitespace
        $username = trim($username);

        // Validate format: letters, numbers, hyphens, underscores, dots
        if (!preg_match('/^[a-zA-Z0-9._-]+$/', $username)) {
            throw new Exception($this->__('Bitbucket username can only contain letters, numbers, dots, hyphens, and underscores'));
        }

        // Check length constraints
        if (strlen($username) < 1) {
            throw new Exception($this->__('Bitbucket username cannot be empty'));
        }

        if (strlen($username) > 30) {
            throw new Exception($this->__('Bitbucket username cannot exceed 30 characters'));
        }

        return $username;
    }

    /**
     * Validate and sanitize Bitbucket app password
     *
     * @param string $appPassword
     * @return string
     * @throws Exception
     */
    protected function _validateAndSanitizeBitbucketPassword($appPassword)
    {
        if (empty($appPassword)) {
            throw new Exception($this->__('Bitbucket App Password is required'));
        }

        // Remove any whitespace
        $appPassword = trim($appPassword);

        // Check for encrypted password placeholder (Magento uses ******)
        if ($appPassword === '******') {
            throw new Exception($this->__('Please enter your Bitbucket App Password to test the connection'));
        }

        // Check minimum length for new passwords (Bitbucket app passwords are typically 22+ characters)
        // But allow shorter passwords for testing purposes - the API will reject invalid ones anyway
        if (strlen($appPassword) < 5) {
            throw new Exception($this->__('Bitbucket App Password appears to be too short. Please ensure you are using an App Password, not your account password.'));
        }

        if (strlen($appPassword) > 100) {
            throw new Exception($this->__('Bitbucket App Password cannot exceed 100 characters'));
        }

        // Basic format validation - should not contain spaces or special chars that could indicate injection
        if (preg_match('/[<>"\'\\\\\x00-\x1f\x7f]/', $appPassword)) {
            throw new Exception($this->__('Bitbucket App Password contains invalid characters'));
        }

        return $appPassword;
    }

    /**
     * Validate and sanitize workspace name
     *
     * @param string $workspace
     * @return string
     * @throws Exception
     */
    protected function _validateAndSanitizeWorkspace($workspace)
    {
        if (empty($workspace)) {
            $workspace = 'pfg'; // Default workspace
        }

        // Remove any whitespace
        $workspace = trim($workspace);

        // Validate format: letters, numbers, hyphens, underscores
        if (!preg_match('/^[a-zA-Z0-9_-]+$/', $workspace)) {
            throw new Exception($this->__('Workspace name can only contain letters, numbers, hyphens, and underscores'));
        }

        // Check length constraints
        if (strlen($workspace) < 1) {
            throw new Exception($this->__('Workspace name cannot be empty'));
        }

        if (strlen($workspace) > 50) {
            throw new Exception($this->__('Workspace name cannot exceed 50 characters'));
        }

        return $workspace;
    }

    /**
     * Validate and sanitize project key
     *
     * @param string $project
     * @return string
     * @throws Exception
     */
    protected function _validateAndSanitizeProject($project)
    {
        if (empty($project)) {
            $project = 'LABS'; // Default project
        }

        // Remove any whitespace
        $project = trim($project);

        // Validate format: letters, numbers, underscores (project keys are typically uppercase)
        if (!preg_match('/^[A-Z0-9_]+$/', $project)) {
            throw new Exception($this->__('Project key can only contain uppercase letters, numbers, and underscores'));
        }

        // Check length constraints
        if (strlen($project) < 1) {
            throw new Exception($this->__('Project key cannot be empty'));
        }

        if (strlen($project) > 20) {
            throw new Exception($this->__('Project key cannot exceed 20 characters'));
        }

        return $project;
    }
}
